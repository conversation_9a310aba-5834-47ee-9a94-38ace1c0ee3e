<template>
  <MyDialog v-model="modelValue" :title="dialogTitle" width="1060px" @ok="handleSave">
    <div :class="e('body')">
      <!-- 顶部表单区域 -->
      <div :class="e('body', 'header')">
        <div>
          <span style="margin-right: 3px">EEC:</span>
          <wui-select v-model="model.currentVxi.eec">
            <wui-option
              v-for="option in eecOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </wui-select>
        </div>
        <div>
          <wui-button @click="handleEditStatusMatrix">Edit Status Matrix</wui-button>
          <wui-button @click="handleEditDiscreteColors">Edit Discrete Label Colors</wui-button>
        </div>
        <div>
          <span style="margin-right: 3px">OMS Type:</span>
          <wui-select v-model="model.currentVxi.omsType">
            <wui-option
              v-for="option in omsTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </wui-select>
        </div>
      </div>

      <!-- 主表格区域 -->
      <div :class="['cfg-setup_table', e('table-container')]">
        <wui-table
          show-overflow-tooltip
          :data="model.currentVxi.channels"
          height="300px"
          border
          @row-contextmenu="handleRowMenu"
        >
          <wui-table-column prop="channels" label="Channels" width="80px" align="center">
            <template #default="{ row }">
              <wui-input v-if="row.flag" v-model="row.channels" placeholder="" />
              <span v-else>{{ row.channels }}</span>
            </template>
          </wui-table-column>

          <wui-table-column prop="type" label="Type" width="150px" align="center">
            <template #default="{ row }">
              <wui-select v-if="row.flag" v-model="row.type" placeholder="">
                <wui-option
                  v-for="option in typeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </wui-select>
              <template v-else>{{ convertType(typeOptions, row.type) }}</template>
            </template>
          </wui-table-column>

          <wui-table-column prop="bus_status" label="Bus Status" width="180px" align="center">
            <template #default="{ row }">
              <wui-input v-if="row.flag" v-model="row.bus_status" placeholder="" />
              <span v-else>{{ row.bus_status }}</span>
            </template>
          </wui-table-column>

          <wui-table-column prop="speed" label="Speed" width="90px" align="center">
            <template #default="{ row }">
              <wui-select v-if="row.flag" v-model="row.speed" placeholder="">
                <wui-option
                  v-for="option in speedOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </wui-select>
              <template v-else>{{ convertType(speedOptions, row.speed) }}</template>
            </template>
          </wui-table-column>

          <wui-table-column prop="parity" label="Parity" width="80px" align="center">
            <template #default="{ row }">
              <wui-select v-if="row.flag" v-model="row.parity" placeholder="">
                <wui-option
                  v-for="option in parityOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </wui-select>
              <template v-else>{{ convertType(parityOptions, row.parity) }}</template>
            </template>
          </wui-table-column>

          <wui-table-column prop="active" label="Active" width="120px" align="center">
            <template #default="{ row }">
              <wui-select v-if="row.flag" v-model="row.active" placeholder="">
                <wui-option
                  v-for="option in activeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </wui-select>
              <template v-else>{{ convertType(activeOptions, row.active) }}</template>
            </template>
          </wui-table-column>

          <wui-table-column prop="channel_id" label="Channel ID" width="80px" align="center">
            <template #default="{ row }">
              <wui-select v-if="row.flag" v-model="row.channel_id" placeholder="">
                <wui-option
                  v-for="option in channelIdOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </wui-select>
              <template v-else>{{ convertType(channelIdOptions, row.channel_id) }}</template>
            </template>
          </wui-table-column>

          <wui-table-column prop="oms_usage" label="OMS Usage" width="120px" align="center">
            <template #default="{ row }">
              <wui-select v-if="row.flag" v-model="row.oms_usage" placeholder="">
                <wui-option
                  v-for="option in omsUsageOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </wui-select>
              <template v-else>{{ convertType(omsUsageOptions, row.oms_usage) }}</template>
            </template>
          </wui-table-column>

          <wui-table-column label="Op" width="100px" align="center">
            <template #default="{ row, $index }">
              <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
            </template>
          </wui-table-column>

          <template #empty>
            <TableTool.Empty />
          </template>
        </wui-table>
      </div>
    </div>
  </MyDialog>
  <DynamicDIalog
    v-if="dynamicShow"
    v-model:visible="dynamicShow"
    :config="dialogConfig"
    :api-callbacks="createApiCallbacks()"
  />
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed, useModel, toRef } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import TableTool, { isAddOrInsertType, OpType, RowType } from '@/renderer/components/TableTool'
import { useBizEngine, useBizMain, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks/bem'
import { convertType } from '@/renderer/utils/common'
import { EngVxiEditorRow, VxiDeviceEditorProps, VxiEecData } from '../consts'
import { NestedDialogContext } from '@/renderer/components/DynamicDIalog/types'
import { getConfigData } from '@/renderer/components/DynamicDIalog/utils'
import DynamicDIalog from '@/renderer/components/DynamicDIalog/index.tsx'

import { DEVICE_ENG_CONFIGS } from './configs'
import {
  conDorActiveType,
  conDorChannelType,
  conDorOmsType,
  conDorOmsUsageType,
  conDorParityType,
  conDorSpeedType
} from '@wuk/cfg'

const { e } = useBem('vxi-editor-e', $styles)

const props = withDefaults(defineProps<VxiDeviceEditorProps>(), {
  modelValue: false,
  deviceData: () => ({
    name: 'CONDOR_429',
    slot: 18,
    address: 123,
    type: 'CONDOR_429'
  })
})

const modelValue = useModel(props, 'modelValue')

const dynamicShow = ref(false)

const dynamicState = ref('DISCRETELABELCOLORS')

const mainPtr = useBizMain()
const engPtr = useBizEngine()

const dialogConfig = ref()

const currentChannel = ref<EngVxiEditorRow | undefined>(undefined)

const dialogCompoted = () => {
  // 传入自己所需要的上下文数据
  const engineName = mainPtr.value?.engine?.name || ''
  const currentVxi = model.currentVxi
  const deviceData = props.deviceData
  const context = {
    engineName,
    currentVxi,
    currentChannel: currentChannel.value,
    deviceData
  }

  return getConfigData(dynamicState.value, DEVICE_ENG_CONFIGS, context)
}

watch(
  () => dynamicShow.value,
  newValue => {
    if (newValue) dialogConfig.value = dialogCompoted()
  }
)

const createApiCallbacks = () => {
  return {
    onFormSubmit: async (formData: Record<string, any>, context?: NestedDialogContext) => {
      if (context?.key && context?.key === 'DISCRETELABELCOLORS') {
        console.log(context, formData)
        return true
      } else if (context?.key && context?.key === 'STATUSMATRIX') {
        console.log(context, formData, 'STATUSMATRIX')
        return true
      }
      return true
    }
  }
}

const dialogTitle = computed(() => {
  const { name, slot, address } = props.deviceData
  return `${name} Device Editor - Slot: ${slot} Address: ${address} EEC: ${model.currentVxi.eec}`
})

const model = reactive<VxiEecData>({
  currentVxi: { eec: 'ALL', channels: [], omsType: 'no_oms' }
})

watch(
  () => model.currentVxi.eec,
  newEec => {
    const current = initialChannels[newEec]
    model.currentVxi.channels = current.channels
    model.currentVxi.omsType = current.omsType
    originList = JSON.parse(JSON.stringify(model.currentVxi.channels))
  }
)

const eecOptions = ref<{ label: string; value: string }[]>([{ label: 'ALL', value: 'ALL' }])

let originList = [] as EngVxiEditorRow[]

const createRow = (row_type: RowType = 'add'): EngVxiEditorRow => ({
  channels: 0,
  type: 'TRANSMITTER',
  bus_status: '',
  speed: 'SLOW',
  parity: 'ODD',
  active: 'INACTIVE',
  channel_id: 'A',
  oms_usage: 'UNUSED',
  flag: true,
  row_type
})

const { handleRowMenu } = useTableCommonMenu(
  toRef(model.currentVxi, 'channels'),
  (key, ...args) => {
    const { row, rowIndex } = args[0]

    switch (key) {
      case 'addKey':
        model.currentVxi.channels.push(createRow('add'))
        break
      case 'insertKey':
        model.currentVxi.channels.splice(rowIndex + 1, 0, createRow('insert'))
        break
      case 'deleteKey':
        handleOp('delete', row, rowIndex)
        break
      case 'modifyKey':
        handleOp('edit', row, rowIndex)
        break
      case 'editChannel':
        handleEditChannel(row, rowIndex)
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' },
    { key: 'editChannel', label: 'Edit Channel' }
  ]
)

const handleOp = (operation: OpType, row: EngVxiEditorRow | undefined, index: number) => {
  if (!row) return
  switch (operation) {
    case 'edit':
      row.flag = true
      break
    case 'cancel':
      handleCancel(row, index)
      break
    case 'delete':
      handleDelete(index)
      break
    case 'select':
      row.flag = false
      break
    default:
      break
  }
}

const handleDelete = async (index: number) => {
  // const res = await bizEngine.value?.removeTimer(index)
  model.currentVxi.channels.splice(index, 1)
  // if (!res) return
  // tipsMessage()
}

const handleCancel = (row: EngVxiEditorRow, index: number): void => {
  if (row.row_type && isAddOrInsertType(row.row_type)) {
    model.currentVxi.channels.splice(index, 1)
    return
  }
  model.currentVxi.channels.splice(index, 1, { ...originList[index], flag: false, row_type: '*' })
}

const handleEditChannel = (row: EngVxiEditorRow | undefined, index: number): void => {
  currentChannel.value = row
  dynamicState.value = 'VXIEDITCHANNEL'
  dynamicShow.value = true
}

const initialChannels: any = {
  ALL: {
    omsType: 'oms_2',
    channels: [
      {
        channels: 1,
        type: 'TRANSMITTER',
        bus_status: 'test_COND123_t1s',
        speed: 'SLOW',
        parity: 'EVEN',
        active: 'INACTIVE',
        channel_id: 'B',
        oms_usage: 'UNUSED'
      },
      {
        channels: 2,
        type: 'TRANSMITTER',
        bus_status: 'test_COND123_t1s',
        speed: 'SLOW',
        parity: 'EVEN',
        active: 'INACTIVE',
        channel_id: 'B',
        oms_usage: 'UNUSED'
      }
    ]
  },
  '12': {
    omsType: 'no_oms',
    channels: [
      {
        channels: 3,
        type: 'TRANSMITTER',
        bus_status: 'test_COND123_t1s',
        speed: 'SLOW',
        parity: 'EVEN',
        active: 'INACTIVE',
        channel_id: 'B',
        oms_usage: 'UNUSED'
      }
    ]
  },
  '34': {
    omsType: 'oms_1',
    channels: [
      {
        channels: 6,
        type: 'TRANSMITTER',
        bus_status: 'test_COND123_t1s',
        speed: 'SLOW',
        parity: 'EVEN',
        active: 'INACTIVE',
        channel_id: 'B',
        oms_usage: 'UNUSED'
      },
      {
        channels: 7,
        type: 'TRANSMITTER',
        bus_status: 'test_COND123_t1s',
        speed: 'SLOW',
        parity: 'EVEN',
        active: 'INACTIVE',
        channel_id: 'B',
        oms_usage: 'UNUSED'
      },
      {
        channels: 8,
        type: 'TRANSMITTER',
        bus_status: 'test_COND123_t1s',
        speed: 'SLOW',
        parity: 'EVEN',
        active: 'INACTIVE',
        channel_id: 'B',
        oms_usage: 'UNUSED'
      }
    ]
  }
}

const getConfigInfo = async () => {
  const { list = [] } = (await engPtr.value?.readDashOptions()) || {}

  eecOptions.value = [
    ...eecOptions.value,
    ...list.map(eec => ({ label: eec.name, value: eec.name }))
  ]

  const current = initialChannels[eecOptions.value[0].value]
  current.channels.forEach((item: any) => {
    item.flag = false
    item.row_type = '*'
  })
  ;(model.currentVxi = {
    channels: current.channels,
    eec: eecOptions.value[0].value,
    omsType: current.omsType
  }),
    (originList = JSON.parse(JSON.stringify(model.currentVxi.channels)))
}

const handleEditStatusMatrix = (): void => {
  dynamicState.value = 'STATUSMATRIX'
  dynamicShow.value = true
}

const handleEditDiscreteColors = (): void => {
  dynamicState.value = 'DISCRETELABELCOLORS'
  dynamicShow.value = true
}

const handleSave = (): void => {
  modelValue.value = false
}

const omsTypeOptions = Object.entries(conDorOmsType).map(([key, value]) => ({
  label: value,
  value: key
}))

const typeOptions = Object.entries(conDorChannelType).map(([key, value]) => ({
  label: value,
  value: key
}))

const speedOptions = Object.entries(conDorSpeedType).map(([key, value]) => ({
  label: value,
  value: key
}))

const parityOptions = Object.entries(conDorParityType).map(([key, value]) => ({
  label: value,
  value: key
}))

const activeOptions = Object.entries(conDorActiveType).map(([key, value]) => ({
  label: value,
  value: key
}))

const channelIdOptions = [
  { label: 'A', value: 'A' },
  { label: 'B', value: 'B' }
]

const omsUsageOptions = Object.entries(conDorOmsUsageType).map(([key, value]) => ({
  label: value,
  value: key
}))

onMounted(() => {
  getConfigInfo()
})
</script>
